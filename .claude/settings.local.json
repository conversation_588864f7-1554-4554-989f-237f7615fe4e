{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(curl:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker-compose ps)", "<PERSON><PERSON>(docker compose:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker cp:*)", "Ba<PERSON>(docker logs mem0-api --tail 10)", "mcp__sequential-thinking__sequentialthinking", "mcp__chrome-mcp__chrome_navigate", "mcp__chrome-mcp__chrome_get_web_content", "mcp__chrome-mcp__chrome_click_element", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(docker logs:*)", "Bash(ls:*)", "Bash(docker system prune:*)", "mcp__firecrawl-mcp__firecrawl_scrape", "mcp__firecrawl-mcp__firecrawl_search", "<PERSON><PERSON>(docker-compose:*)", "Bash(pip cache:*)", "Bash(pip install:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我对海鲜过敏，不能吃虾蟹\"\"}],\n    \"\"user_id\"\": \"\"test_user\"\"\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"query\"\": \"\"饮食偏好\"\",\n    \"\"user_id\"\": \"\"test_user\"\",\n    \"\"keyword_search\"\": true\n  }')", "mcp__shrimp-task-manager__list_tasks", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(chmod:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(touch:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"I love playing tennis on weekends\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"test_user\"\"\n}')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON> and I love pizza\"\"}],\n    \"\"user_id\"\": \"\"test_user_timestamp\"\",\n    \"\"timestamp\"\": 1672531200,\n    \"\"infer\"\": false\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I visited Paris in March 2024\"\"}],\n    \"\"user_id\"\": \"\"test_user_timestamp\"\",\n    \"\"timestamp\"\": 1709251200\n  }')", "<PERSON><PERSON>(cat:*)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d @/tmp/multimodal_doc_test.json)", "Bash(node:*)", "Bash(pip3 install Pillow)", "Bash(./run-standalone.sh run -d)", "<PERSON><PERSON>(timeout:*)", "Bash(cp:*)", "Bash(sqlite3:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(docker volume inspect:*)", "Bash(docker volume:*)", "Bash(docker container logs:*)", "Bash(ss:*)", "Bash(kill:*)", "<PERSON><PERSON>(sudo mkdir:*)", "Bash(sudo chown -R 1000:1000 /var/lib/mem0/data)", "Bash(sudo chmod -R 755 /var/lib/mem0/data)", "Bash(sudo chown -R 1000:1000 /var/lib/mem0)", "Bash(sudo chmod -R 755 /var/lib/mem0)", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": {\n          \"\"type\"\": \"\"pdf_url\"\",\n          \"\"pdf_url\"\": {\n            \"\"url\"\": \"\"https://www.adobe.com/support/products/enterprise/knowledgecenter/media/c4611_sample_explain.pdf\"\"\n          }\n        }\n      }\n    ],\n    \"\"user_id\"\": \"\"test_pdf_user2\"\",\n    \"\"metadata\"\": {\"\"test_type\"\": \"\"pdf_document\"\", \"\"source\"\": \"\"adobe_sample\"\"}\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n    \"\"messages\"\": [\n      {\n        \"\"role\"\": \"\"user\"\", \n        \"\"content\"\": {\n          \"\"type\"\": \"\"mdx_url\"\",\n          \"\"mdx_url\"\": {\n            \"\"url\"\": \"\"data:text/plain;base64,6L+Z5piv5LiA5Liq5oqA5pyv5paH5qGj55qE5YaF5a6544CC5paH5qGj5Lit5YyF5ZCr5Lul5LiL6YeN6KaB5L+h5oGv77yaCjEuIOS6uuW3peaZuuiDveeahOWfuuacrOamguW/teWSjOWPkeWxleWOhueoiwoyLiDmnLrlmajlrabkuaDnrpfms5XnmoTliIbnsbvvvJrnm5HnnaPlrabkuaDjgIHml6Dnm5HnnaPlrabkuaDjgIHlvLrljJblrabkuaAKMy4g5rex5bqm5a2m5Lmg5Zyo5Zu+5YOP6K+G5Yir44CB6Ieq54S26K+t6KiA5aSE55CG5Lit55qE5bqU55SoCjQuIOWkp+Wei+ivreiogOaooeWei+eahOW3peS9nOWOn+eQhuWSjOS8mOWMluaWueazlQo1LiBBSeS8pueQhuWSjOWuieWFqOaAp+iAg+iZkQoK6L+Z5Lqb5YaF5a655a+55LqO55CG6Kej546w5LujQUnmioDmnK/pnZ7luLjph43opoHjgIIK\"\"\n          }\n        }\n      }\n    ],\n    \"\"user_id\"\": \"\"test_mdx_user\"\",\n    \"\"metadata\"\": {\"\"test_type\"\": \"\"mdx_document\"\", \"\"source\"\": \"\"base64_text\"\"}\n  }')", "Bash(-H \"Content-Type: application/json\" )", "Bash(-d '{\n  \"\"query\"\": \"\"人工智能和机器学习的技术概念\"\",\n  \"\"user_id\"\": \"\"multimodal_test_user\"\",\n  \"\"limit\"\": 10\n}')", "Bash(-H \"Content-Type: application/json\")", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": [\n        {\n          \"\"type\"\": \"\"text\"\",\n          \"\"text\"\": \"\"我正在学习Mem0记忆系统的架构和核心概念。Mem0是一个智能记忆管理平台，具有以下特点：1) 支持多种LLM提供商如OpenAI、Anthropic等；2) 具备向量存储和语义搜索功能；3) 支持用户、代理和会话级别的记忆作用域；4) 提供RESTful API接口；5) 支持多模态内容处理包括文本、图像和文档。\"\"\n        }\n      ]\n    }\n  ],\n  \"\"user_id\"\": \"\"multimodal_test_user\"\"\n}')", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"今天我了解到Mem0是一个强大的记忆管理系统，支持多模态内容处理。\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"multimodal_test_user\"\"\n}')", "Bash(-d '{\n  \"\"query\"\": \"\"平静放松的感受\"\",\n  \"\"user_id\"\": \"\"multimodal_test_user\"\",\n  \"\"limit\"\": 3\n}')", "Bash(-d '{\n  \"\"query\"\": \"\"Python机器学习\"\",\n  \"\"user_id\"\": \"\"test_memory_id_user\"\",\n  \"\"limit\"\": 3\n}')", "<PERSON><PERSON>(echo:*)", "Bash(echo -e \"\\n\\n测试3: 存在的UUID\")", "Bash(-d '{\n  \"\"messages\"\": [\n    {\n      \"\"role\"\": \"\"user\"\", \n      \"\"content\"\": \"\"我今天参加了一个重要的商务会议，讨论了新项目的合作方案。\"\"\n    }\n  ],\n  \"\"user_id\"\": \"\"category_test_user\"\",\n  \"\"custom_categories\"\": [\n    {\"\"business\"\": \"\"商务相关活动和决策\"\"},\n    {\"\"meetings\"\": \"\"会议和讨论记录\"\"}\n  ]\n}')", "Bash(-d '{\n  \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我报名了一个Python编程课程，想要深入学习人工智能技术\"\"}],\n  \"\"user_id\"\": \"\"test_user_categories\"\",\n  \"\"custom_categories\"\": [\n    {\"\"学习\"\": \"\"学习计划、教育相关\"\"},\n    {\"\"编程\"\": \"\"编程语言、代码开发\"\"}\n  ]\n}')", "Bash(-d '{\n  \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我在网上购物平台买了一本关于深度学习的书籍\"\"}],\n  \"\"user_id\"\": \"\"test_user_categories\"\"\n}')", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(mkdir:*)", "mcp__chrome-mcp__chrome_screenshot", "mcp__chrome-mcp__chrome_console", "Bash(npm run dev:*)", "mcp__chrome-mcp__get_windows_and_tabs", "mcp__chrome-mcp__chrome_network_debugger_start", "mcp__chrome-mcp__chrome_network_debugger_stop", "mcp__chrome-mcp__chrome_network_request", "Bash(npm run build:*)", "mcp__chrome-mcp__chrome_inject_script", "mcp__chrome-mcp__chrome_get_interactive_elements", "mcp__chrome-mcp__chrome_network_capture_start", "mcp__chrome-mcp__chrome_network_capture_stop", "mcp__chrome-mcp__chrome_send_command_to_inject_script", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"<PERSON> works at OpenAI as a software engineer. She collaborates with <PERSON> on machine learning projects.\"\"}],\n    \"\"user_id\"\": \"\"demo_user\"\"\n  }')", "Bash(docker stack:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm mem0-api mem0-neo4j mem0-qdrant)", "Bash(docker network:*)", "Bash(# 设置mem0数据目录权限（对应容器内mem0用户，一般UID 1000）\nchown -R 1000:1000 /opt/mem0ai/data/mem0\n\n# 设置qdrant数据目录权限（qdrant容器默认用户）\nchown -R 1000:1000 /opt/mem0ai/data/qdrant  \n\n# 设置neo4j数据目录权限（neo4j容器默认UID 7474）\nchown -R 7474:7474 /opt/mem0ai/data/neo4j\n\n# 设置目录权限\nchmod -R 755 /opt/mem0ai/data/\n\nls -la /opt/mem0ai/data/)", "Bash(/opt/mem0ai/setup-permissions.sh)", "Bash(chown -R root:root /opt/mem0ai/data/mem0 /opt/mem0ai/data/qdrant)", "Bash(/opt/mem0ai/server/deploy.sh start)", "Bash(./deploy.sh start --no-permissions)", "Bash(sudo ./deploy-v2.sh start)", "<PERSON><PERSON>(sudo docker compose:*)", "Bash(sudo docker system prune:*)", "Bash(./deploy-v2.sh:*)", "<PERSON><PERSON>(sudo docker:*)", "Bash(sudo chown:*)", "Bash(sudo chmod:*)", "Bash(hatch run lint:*)", "Bash(make lint:*)", "Bash(-H \"accept: application/json\" )", "Bash(-G -d \"limit=100\")", "Bash(-H \"accept: application/json\" )", "Bash(-d '{}')", "Bash(sudo rm -rf data/*)", "Bash(-H \"accept: application/json\")", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"My name is <PERSON>. I love cats and work as a designer.\"\"}],\n    \"\"user_id\"\": \"\"alice\"\"\n  }')", "Bash(sudo rm -f /opt/mem0ai/data/history.db /opt/mem0ai/data/mem0/history.db)", "WebFetch(domain:localhost)", "Bash(./verify-setup.sh:*)", "Bash(-d '{\n    \"\"messages\"\": [\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我喜欢吃意大利面和寿司\"\"},\n      {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"我记住了你喜欢意大利面和寿司\"\"}\n    ],\n    \"\"user_id\"\": \"\"test_user_001\"\",\n    \"\"custom_categories\"\": [\n      {\"\"food_preference\"\": \"\"用户的食物偏好和饮食习惯\"\"},\n      {\"\"personal_taste\"\": \"\"个人品味和喜好\"\"}\n    ]\n  }')", "Bash(if [ -f /opt/mem0ai/server/.env ])", "<PERSON><PERSON>(then)", "<PERSON><PERSON>(else)", "Bash(fi)", "Bash(__NEW_LINE__ echo \"\")", "Bash(if [ -f /opt/mem0ai/server/.env.prod ])", "Bash(./deploy.sh:*)", "Bash(./test_data_validation_control.sh:*)", "Bash(./cleanup_uncategorized_memories.sh)", "Bash(for id in \"00b3ae5e-1dfe-476c-8603-253677736e76\" \"3a030fbc-67e7-410d-b2ab-2c8df21b0d1f\" \"52afd10b-faeb-4cbd-b5dd-1634adb1defb\" \"90b0e255-5105-4a06-a1f2-302e0e1718ca\" \"94e65b90-4e6a-41a9-84d8-fc74729f3e7d\" \"bf945be4-3a2c-4739-afdf-4ea49b72cf7e\" \"faf6d575-508c-464f-819d-6c5e97e4b677\")", "Bash(do)", "Bash(if [ $? -eq 0 ])", "Bash(done)", "Bash(./batch_delete_uncategorized.sh)", "Bash(/tmp/analyze_endpoints.sh:*)", "Bash(./fix_api_endpoints.sh:*)", "Bash(bash:*)", "Bash(-d '{\n    \"\"messages\"\": [\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I love hiking in the mountains\"\"},\n      {\"\"role\"\": \"\"assistant\"\", \"\"content\"\": \"\"That sounds wonderful! What is your favorite hiking trail?\"\"},\n      {\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"I enjoy the Pacific Crest Trail\"\"}\n    ],\n    \"\"user_id\"\": \"\"auto_test_user\"\"\n  }')", "Bash(for i in {1..4})", "Bash(do curl -s -X POST \"http://localhost:8000/v1/memories/\" -H \"Content-Type: application/json\" -d @/tmp/memory$i.json)", "Bash(-d @/tmp/feedback_very_negative.json)", "Bash(-d @/tmp/multimodal_search_pizza.json)", "Bash(-d @/tmp/multimodal_simple_test.json)", "Bash(-d @/tmp/search_cross_modal.json)", "<PERSON><PERSON>(printenv)", "Bash(OPENAI_API_KEY=dummy python3 -c \"\nimport sys\nsys.path.append(''/opt/mem0ai'')\nfrom mem0 import Memory\n\ntry:\n    m = Memory()\n    print(''Embedder config type:'', type(m.config.embedder.config))\n    print(''Embedder config:'', m.config.embedder.config)\n    if hasattr(m.config.embedder.config, ''model''):\n        print(''Has model attribute:'', m.config.embedder.config.model)\n    else:\n        print(''No model attribute, checking keys:'', list(m.config.embedder.config.keys()) if hasattr(m.config.embedder.config, ''keys'') else ''Not dict-like'')\n        print(''Dict value for model:'', m.config.embedder.config.get(''model'', ''KEY_NOT_FOUND''))\nexcept Exception as e:\n    print(''Error:'', e)\n    import traceback\n    traceback.print_exc()\n\")", "Bash(git checkout:*)", "WebFetch(domain:github.com)", "WebFetch(domain:docs.mem0.ai)", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"测试修复后的embedding配置\"\"}],\n    \"\"user_id\"\": \"\"test_fix_user\"\"\n  }')", "Bash(-d '{\"\"data\"\": \"\"Updated test memory\"\", \"\"metadata\"\": {\"\"test\"\": true}}' )", "Bash(-w \"\\nHTTP Status: %{http_code}\\n\")", "Bash(-d '{\"\"text\"\": \"\"Updated test memory\"\", \"\"metadata\"\": {\"\"test\"\": true}}' )", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"测试添加操作调试\"\"}],\n    \"\"user_id\"\": \"\"debug_test_user\"\"\n  }' )", "Bash(-d \"{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"今天是$(date)，我正在测试历史记录功能\"\"}],\n    \"\"user_id\"\": \"\"history_test_user_$(date +%s)\"\"\n  }\")", "Bash(-d '{\n    \"\"messages\"\": [{\"\"role\"\": \"\"user\"\", \"\"content\"\": \"\"我的生日是1990年5月15日，我很喜欢天文学\"\"}],\n    \"\"user_id\"\": \"\"debug_personal_info\"\"\n  }')"], "deny": []}}