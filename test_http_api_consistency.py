#!/usr/bin/env python3
"""
HTTP API记忆操作数据一致性测试脚本

通过真实的HTTP API调用测试添加、搜索、删除、更新操作的数据一致性，
验证API响应与历史数据库记录之间的对应关系。
"""

import os
import requests
import json
import time
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

class HTTPMemoryAPIConsistencyTest:
    """HTTP API记忆操作数据一致性测试类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """初始化测试环境"""
        self.base_url = base_url
        self.test_user_id = f"test_user_{int(time.time())}"
        self.test_memories = []  # 存储测试创建的记忆ID和信息
        
        # 使用环境变量配置数据库路径，提供默认值和多个候选路径
        self.db_path = self._get_database_path()
        print(f"🗄️  使用数据库路径: {self.db_path}")
        
        # 测试结果统计
        self.test_results = {
            "api_health": {"success": 0, "failed": 0, "details": []},
            "add": {"success": 0, "failed": 0, "details": []},
            "search": {"success": 0, "failed": 0, "details": []},
            "update": {"success": 0, "failed": 0, "details": []},
            "delete": {"success": 0, "failed": 0, "details": []},
            "consistency": {"success": 0, "failed": 0, "details": []}
        }
    
    def _get_database_path(self) -> str:
        """
        根据环境变量和可用路径确定数据库文件位置
        
        优先级：
        1. HISTORY_DB_PATH 环境变量
        2. MEM0_HISTORY_DB_PATH 环境变量  
        3. 基于MEM0_DATA_PATH的路径构建
        4. 候选路径列表中第一个存在的文件
        """
        # 1. 直接从环境变量获取
        env_paths = [
            os.getenv('HISTORY_DB_PATH'),
            os.getenv('MEM0_HISTORY_DB_PATH'),
        ]
        
        for path in env_paths:
            if path and os.path.exists(path):
                return path
        
        # 2. 基于数据目录构建路径
        data_dir = os.getenv('MEM0_DATA_PATH', os.getenv('MEM0_DIR', ''))
        if data_dir:
            candidate_path = os.path.join(data_dir, 'history.db')
            if os.path.exists(candidate_path):
                return candidate_path
        
        # 3. 候选路径列表（基于已知的项目结构）
        candidate_paths = [
            "/opt/mem0ai/server/data/mem0/history.db",  # 容器映射路径
            "/opt/mem0ai/server/data/history.db",       # 备选容器路径
            "/opt/mem0ai/data/history.db",              # 主机直接路径
            "./data/history.db",                        # 相对路径
            "./server/data/mem0/history.db",            # 项目结构路径
        ]
        
        for path in candidate_paths:
            if os.path.exists(path):
                return path
        
        # 4. 如果都不存在，返回最可能的路径（让后续测试显示具体错误）
        return candidate_paths[0]
    
    def check_api_health(self) -> bool:
        """检查API服务健康状态"""
        try:
            print("🏥 检查API服务状态...")
            response = requests.get(f"{self.base_url}/health", timeout=10)
            
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API服务正常运行: {health_data}")
                self.test_results["api_health"]["success"] = 1
                return True
            else:
                print(f"❌ API服务返回错误状态码: {response.status_code}")
                self.test_results["api_health"]["failed"] = 1
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ API服务连接失败: {e}")
            self.test_results["api_health"]["failed"] = 1
            self.test_results["api_health"]["details"].append(f"连接失败: {e}")
            return False
    
    def test_add_memories_via_api(self) -> bool:
        """通过API测试添加记忆操作"""
        print("\n📝 测试HTTP API添加记忆操作...")
        
        # 使用独特且有意义的个人化信息，避免LLM过滤
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = int(timestamp[-6:])  # 6位数字ID
        test_messages = [
            {"role": "user", "content": f"我的身份证号码是11010819900{unique_id%100:02d}123X，目前居住在上海浦东新区陆家嘴金茂大厦"},
            {"role": "user", "content": f"我刚刚入职字节跳动公司，担任AI算法工程师，工号是BD{unique_id}，年薪80万"},
            {"role": "user", "content": f"我有严重的花粉过敏症，对猫毛也过敏，医保卡号是{unique_id*7%999999999:09d}"},
            {"role": "user", "content": f"我预订了{timestamp[:4]}年12月25日从上海到东京的航班，座位号14A，航班号NH920"},
            {"role": "user", "content": f"我正在清华大学人工智能学院攻读博士学位，导师是李明教授，学号是{timestamp[-8:]}"}
        ]
        
        for i, message in enumerate(test_messages):
            try:
                # 构建API请求
                api_data = {
                    "messages": [message],
                    "user_id": self.test_user_id,
                    "metadata": {
                        "test_index": i,
                        "operation": "api_add_test",
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
                print(f"  📤 发送添加请求 {i+1}: {message['content'][:30]}...")
                
                # 发送HTTP POST请求
                response = requests.post(
                    f"{self.base_url}/v1/memories/",
                    json=api_data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # API直接返回数组格式，不是{"results": []}包装格式
                    if isinstance(result, list):
                        if result:  # 非空数组 - 添加了新记忆
                            memory_item = result[0]  # 取第一个记忆项
                            memory_id = memory_item.get("id")
                            
                            if memory_id:
                                # 存储测试记忆信息
                                self.test_memories.append({
                                    "id": memory_id,
                                    "content": message["content"],
                                    "api_response": memory_item,
                                    "operation": "add"
                                })
                                
                                # 验证API响应与数据库的一致性
                                if self._verify_add_api_consistency(memory_id, memory_item, message["content"]):
                                    self.test_results["add"]["success"] += 1
                                    print(f"  ✅ 添加请求 {i+1} 成功且数据一致")
                                else:
                                    self.test_results["add"]["failed"] += 1
                                    print(f"  ❌ 添加请求 {i+1} 数据不一致")
                            else:
                                self.test_results["add"]["failed"] += 1
                                print(f"  ❌ 添加请求 {i+1} 响应缺少memory_id")
                        else:  # 空数组 - LLM判断为重复内容，未添加新记忆
                            print(f"  ⚠️  添加请求 {i+1} 返回空数组（LLM智能过滤重复/无意义内容）")
                            # 这种情况是正常的LLM行为，不算失败
                            # 但也不算成功添加新记忆，所以我们跳过验证
                            pass  # 既不增加成功计数，也不增加失败计数
                    else:
                        self.test_results["add"]["failed"] += 1
                        print(f"  ❌ 添加请求 {i+1} 响应格式错误: 期望数组，实际收到 {type(result)}")
                        self.test_results["add"]["details"].append(f"请求{i+1}: 响应格式错误")
                        
                else:
                    self.test_results["add"]["failed"] += 1
                    error_msg = f"API返回错误状态码: {response.status_code}"
                    try:
                        error_detail = response.json()
                        error_msg += f", 详情: {error_detail}"
                    except:
                        error_msg += f", 响应: {response.text[:200]}"
                    
                    print(f"  ❌ 添加请求 {i+1} 失败: {error_msg}")
                    self.test_results["add"]["details"].append(error_msg)
                
                # 请求间隔，避免过快
                time.sleep(1)
                
            except requests.exceptions.RequestException as e:
                self.test_results["add"]["failed"] += 1
                error_msg = f"请求异常: {e}"
                print(f"  ❌ 添加请求 {i+1} 异常: {error_msg}")
                self.test_results["add"]["details"].append(error_msg)
            except Exception as e:
                self.test_results["add"]["failed"] += 1
                error_msg = f"处理异常: {e}"
                print(f"  ❌ 添加请求 {i+1} 处理异常: {error_msg}")
                self.test_results["add"]["details"].append(error_msg)
        
        success_count = self.test_results["add"]["success"]
        total_count = success_count + self.test_results["add"]["failed"]
        print(f"📊 添加操作测试完成: {success_count}/{total_count} 成功")
        
        return success_count > 0
    
    def test_search_memories_via_api(self) -> bool:
        """通过API测试搜索记忆操作"""
        print("\n🔍 测试HTTP API搜索记忆操作...")
        
        if not self.test_memories:
            print("  ⚠️  没有可搜索的测试记忆")
            return False
        
        search_queries = [
            "喜欢的食物",
            "工作经验",
            "兴趣爱好", 
            "旅游计划",
            "学习内容"
        ]
        
        for i, query in enumerate(search_queries):
            try:
                print(f"  🔎 执行搜索 {i+1}: {query}")
                
                # 构建搜索API请求
                search_data = {
                    "query": query,
                    "user_id": self.test_user_id,
                    "limit": 10
                }
                
                # 发送HTTP POST请求
                response = requests.post(
                    f"{self.base_url}/v1/memories/search/",
                    json=search_data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                
                if response.status_code == 200:
                    search_results = response.json()
                    
                    # API directly returns array of results
                    if isinstance(search_results, list):
                        # 验证搜索API响应与数据库的一致性
                        if self._verify_search_api_consistency(query, search_results):
                            self.test_results["search"]["success"] += 1
                            print(f"  ✅ 搜索 {i+1} 成功，找到 {len(search_results)} 条结果")
                        else:
                            self.test_results["search"]["failed"] += 1
                            print(f"  ❌ 搜索 {i+1} 数据不一致")
                    else:
                        self.test_results["search"]["failed"] += 1
                        print(f"  ❌ 搜索 {i+1} 响应格式错误: 期望数组，实际收到 {type(search_results)}")
                        
                else:
                    self.test_results["search"]["failed"] += 1
                    error_msg = f"搜索API返回错误: {response.status_code}"
                    print(f"  ❌ 搜索 {i+1} 失败: {error_msg}")
                    self.test_results["search"]["details"].append(error_msg)
                
                time.sleep(1)
                
            except Exception as e:
                self.test_results["search"]["failed"] += 1
                error_msg = f"搜索异常: {e}"
                print(f"  ❌ 搜索 {i+1} 异常: {error_msg}")
                self.test_results["search"]["details"].append(error_msg)
        
        success_count = self.test_results["search"]["success"]
        total_count = success_count + self.test_results["search"]["failed"]
        print(f"📊 搜索操作测试完成: {success_count}/{total_count} 成功")
        
        return success_count > 0
    
    def test_update_memories_via_api(self) -> bool:
        """通过API测试更新记忆操作"""
        print("\n🔄 测试HTTP API更新记忆操作...")
        
        if len(self.test_memories) < 2:
            print("  ⚠️  没有足够的测试记忆进行更新")
            return False
        
        # 选择前2个记忆进行更新测试
        memories_to_update = self.test_memories[:2]
        
        for i, memory_info in enumerate(memories_to_update):
            try:
                memory_id = memory_info["id"]
                original_content = memory_info["content"]
                new_content = f"【已更新】{original_content} - 修改时间: {datetime.now().strftime('%H:%M:%S')}"
                
                print(f"  🔄 更新记忆 {i+1}: {memory_id[:8]}...")
                
                # 构建更新API请求
                update_data = {
                    "text": new_content,
                    "metadata": {
                        "updated_via_api": True,
                        "update_timestamp": datetime.now().isoformat()
                    }
                }
                
                # 发送HTTP PUT请求
                response = requests.put(
                    f"{self.base_url}/v1/memories/{memory_id}/",
                    json=update_data,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 验证更新API响应与数据库的一致性
                    if self._verify_update_api_consistency(memory_id, new_content, result):
                        self.test_results["update"]["success"] += 1
                        print(f"  ✅ 更新 {i+1} 成功且数据一致")
                    else:
                        self.test_results["update"]["failed"] += 1
                        print(f"  ❌ 更新 {i+1} 数据不一致")
                        
                else:
                    self.test_results["update"]["failed"] += 1
                    error_msg = f"更新API返回错误: {response.status_code}"
                    print(f"  ❌ 更新 {i+1} 失败: {error_msg}")
                    self.test_results["update"]["details"].append(error_msg)
                
                time.sleep(1)
                
            except Exception as e:
                self.test_results["update"]["failed"] += 1
                error_msg = f"更新异常: {e}"
                print(f"  ❌ 更新 {i+1} 异常: {error_msg}")
                self.test_results["update"]["details"].append(error_msg)
        
        success_count = self.test_results["update"]["success"]
        total_count = success_count + self.test_results["update"]["failed"]
        print(f"📊 更新操作测试完成: {success_count}/{total_count} 成功")
        
        return success_count > 0
    
    def test_delete_memories_via_api(self) -> bool:
        """通过API测试删除记忆操作"""
        print("\n🗑️ 测试HTTP API删除记忆操作...")
        
        if len(self.test_memories) < 2:
            print("  ⚠️  没有足够的测试记忆进行删除")
            return False
        
        # 选择最后2个记忆进行删除测试
        memories_to_delete = self.test_memories[-2:]
        
        for i, memory_info in enumerate(memories_to_delete):
            try:
                memory_id = memory_info["id"]
                
                print(f"  🗑️ 删除记忆 {i+1}: {memory_id[:8]}...")
                
                # 发送HTTP DELETE请求
                response = requests.delete(
                    f"{self.base_url}/v1/memories/{memory_id}",
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 验证删除API响应与数据库的一致性
                    if self._verify_delete_api_consistency(memory_id, result):
                        self.test_results["delete"]["success"] += 1
                        print(f"  ✅ 删除 {i+1} 成功且数据一致")
                    else:
                        self.test_results["delete"]["failed"] += 1
                        print(f"  ❌ 删除 {i+1} 数据不一致")
                        
                else:
                    self.test_results["delete"]["failed"] += 1
                    error_msg = f"删除API返回错误: {response.status_code}"
                    print(f"  ❌ 删除 {i+1} 失败: {error_msg}")
                    self.test_results["delete"]["details"].append(error_msg)
                
                time.sleep(1)
                
            except Exception as e:
                self.test_results["delete"]["failed"] += 1
                error_msg = f"删除异常: {e}"
                print(f"  ❌ 删除 {i+1} 异常: {error_msg}")
                self.test_results["delete"]["details"].append(error_msg)
        
        success_count = self.test_results["delete"]["success"]
        total_count = success_count + self.test_results["delete"]["failed"]
        print(f"📊 删除操作测试完成: {success_count}/{total_count} 成功")
        
        return success_count > 0
    
    def _verify_add_api_consistency(self, memory_id: str, api_response: dict, content: str) -> bool:
        """验证添加操作的API响应与数据库一致性"""
        try:
            # 等待数据库同步
            time.sleep(0.5)
            
            # 检查历史数据库中的记录
            history_records = self._get_memory_history_from_db(memory_id)
            
            if not history_records:
                self.test_results["consistency"]["details"].append(f"记忆 {memory_id} 没有历史记录")
                return False
            
            # 查找ADD事件
            add_records = [r for r in history_records if r.get("event") == "ADD"]
            if not add_records:
                self.test_results["consistency"]["details"].append(f"记忆 {memory_id} 缺少ADD历史事件")
                return False
            
            add_record = add_records[0]
            
            # 验证基本字段完整性
            required_fields = ["role", "metadata"]
            for field in required_fields:
                if not add_record.get(field):
                    self.test_results["consistency"]["details"].append(f"ADD记录缺少 {field} 字段")
                    return False
            
            # 验证内容一致性
            if add_record.get("new_memory") != content:
                self.test_results["consistency"]["details"].append("ADD记录内容与API请求不一致")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["consistency"]["details"].append(f"验证ADD一致性异常: {e}")
            return False
    
    def _verify_search_api_consistency(self, query: str, search_results: list) -> bool:
        """验证搜索操作的API响应与数据库一致性"""
        try:
            # 等待数据库同步
            time.sleep(0.5)
            
            # 检查最近的搜索历史记录
            recent_search_records = self._get_recent_search_records_from_db()
            
            # 查找匹配的搜索记录
            matching_record = None
            for record in recent_search_records:
                if record.get("new_memory") == query:
                    matching_record = record
                    break
            
            if not matching_record:
                self.test_results["consistency"]["details"].append(f"未找到查询 '{query}' 的历史记录")
                return False
            
            # 验证搜索记录完整性
            required_fields = ["role", "metadata"]
            for field in required_fields:
                if not matching_record.get(field):
                    self.test_results["consistency"]["details"].append(f"搜索记录缺少 {field} 字段")
                    return False
            
            # 验证metadata中的结果数量
            try:
                metadata = json.loads(matching_record.get("metadata", "{}"))
                db_results_count = metadata.get("results_count", -1)
                api_results_count = len(search_results)
                
                if db_results_count != api_results_count:
                    self.test_results["consistency"]["details"].append(
                        f"搜索结果数量不一致: API={api_results_count}, DB={db_results_count}"
                    )
                    return False
                    
            except json.JSONDecodeError:
                self.test_results["consistency"]["details"].append("搜索记录metadata格式错误")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["consistency"]["details"].append(f"验证搜索一致性异常: {e}")
            return False
    
    def _verify_update_api_consistency(self, memory_id: str, new_content: str, api_response: dict) -> bool:
        """验证更新操作的API响应与数据库一致性"""
        try:
            # 等待数据库同步
            time.sleep(0.5)
            
            # 检查历史数据库中的记录
            history_records = self._get_memory_history_from_db(memory_id)
            
            # 查找UPDATE事件
            update_records = [r for r in history_records if r.get("event") == "UPDATE"]
            if not update_records:
                self.test_results["consistency"]["details"].append(f"记忆 {memory_id} 缺少UPDATE历史事件")
                return False
            
            update_record = update_records[-1]  # 最新的更新记录
            
            # 验证UPDATE记录完整性
            required_fields = ["role", "metadata", "new_memory"]
            for field in required_fields:
                if not update_record.get(field):
                    self.test_results["consistency"]["details"].append(f"UPDATE记录缺少 {field} 字段")
                    return False
            
            # 验证更新内容
            if update_record.get("new_memory") != new_content:
                self.test_results["consistency"]["details"].append("UPDATE记录内容与API请求不一致")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["consistency"]["details"].append(f"验证更新一致性异常: {e}")
            return False
    
    def _verify_delete_api_consistency(self, memory_id: str, api_response: dict) -> bool:
        """验证删除操作的API响应与数据库一致性"""
        try:
            # 等待数据库同步
            time.sleep(0.5)
            
            # 检查历史数据库中的记录
            history_records = self._get_memory_history_from_db(memory_id)
            
            # 查找DELETE事件
            delete_records = [r for r in history_records if r.get("event") == "DELETE"]
            if not delete_records:
                self.test_results["consistency"]["details"].append(f"记忆 {memory_id} 缺少DELETE历史事件")
                return False
            
            delete_record = delete_records[-1]  # 最新的删除记录
            
            # 验证DELETE记录完整性
            required_fields = ["role", "metadata", "old_memory"]
            for field in required_fields:
                if not delete_record.get(field):
                    self.test_results["consistency"]["details"].append(f"DELETE记录缺少 {field} 字段")
                    return False
            
            # 验证is_deleted标志
            if delete_record.get("is_deleted") != 1:
                self.test_results["consistency"]["details"].append("DELETE记录is_deleted标志错误")
                return False
            
            return True
            
        except Exception as e:
            self.test_results["consistency"]["details"].append(f"验证删除一致性异常: {e}")
            return False
    
    def _get_memory_history_from_db(self, memory_id: str) -> List[Dict]:
        """从数据库获取指定记忆的历史记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM history 
                WHERE memory_id = ? 
                ORDER BY created_at DESC
            """, (memory_id,))
            
            columns = [description[0] for description in cursor.description]
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                records.append(record)
            
            conn.close()
            return records
            
        except Exception as e:
            print(f"获取记忆历史失败: {e}")
            return []
    
    def _get_recent_search_records_from_db(self) -> List[Dict]:
        """从数据库获取最近的搜索记录"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM history 
                WHERE event = 'SEARCH' 
                AND created_at > datetime('now', '-2 minute')
                ORDER BY created_at DESC
                LIMIT 20
            """)
            
            columns = [description[0] for description in cursor.description]
            records = []
            for row in cursor.fetchall():
                record = dict(zip(columns, row))
                records.append(record)
            
            conn.close()
            return records
            
        except Exception as e:
            print(f"获取搜索历史失败: {e}")
            return []
    
    def generate_comprehensive_report(self) -> float:
        """生成综合测试报告"""
        print("\n" + "="*80)
        print("📋 HTTP API记忆操作数据一致性测试报告")
        print("="*80)
        
        # 计算总体统计
        total_success = sum(result["success"] for result in self.test_results.values())
        total_failed = sum(result["failed"] for result in self.test_results.values())
        total_tests = total_success + total_failed
        
        success_rate = (total_success / total_tests * 100) if total_tests > 0 else 0
        
        print(f"📊 总体统计:")
        print(f"   - 总测试数: {total_tests}")
        print(f"   - 成功: {total_success}")
        print(f"   - 失败: {total_failed}")
        print(f"   - 成功率: {success_rate:.1f}%")
        
        print(f"\n🔍 详细结果:")
        
        test_categories = [
            ("api_health", "API健康检查"),
            ("add", "添加记忆操作"),
            ("search", "搜索记忆操作"),
            ("update", "更新记忆操作"),
            ("delete", "删除记忆操作")
        ]
        
        for category, name in test_categories:
            result = self.test_results[category]
            total = result["success"] + result["failed"]
            if total > 0:
                rate = result["success"] / total * 100
                status = "✅" if rate == 100 else "⚠️" if rate >= 80 else "❌"
                print(f"   {status} {name}: {result['success']}/{total} ({rate:.1f}%)")
                
                # 显示前3个错误详情
                if result["details"]:
                    print(f"     错误详情:")
                    for detail in result["details"][:3]:
                        print(f"       - {detail}")
        
        # 数据库状态统计
        print(f"\n📈 数据库一致性统计:")
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 统计最近3分钟的记录质量
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN role IS NOT NULL AND role != '' THEN 1 ELSE 0 END) as with_role,
                    SUM(CASE WHEN metadata IS NOT NULL AND metadata != '' THEN 1 ELSE 0 END) as with_metadata,
                    SUM(CASE WHEN actor_id IS NOT NULL AND actor_id != '' THEN 1 ELSE 0 END) as with_actor,
                    COUNT(DISTINCT event) as event_types
                FROM history 
                WHERE created_at > datetime('now', '-3 minute')
                AND memory_id LIKE '%'
            """)
            
            stats = cursor.fetchone()
            if stats and stats[0] > 0:
                total, with_role, with_metadata, with_actor, event_types = stats
                print(f"   - 最近3分钟记录总数: {total}")
                print(f"   - 包含role字段: {with_role}/{total} ({with_role/total*100:.1f}%)")
                print(f"   - 包含metadata字段: {with_metadata}/{total} ({with_metadata/total*100:.1f}%)")
                print(f"   - 包含actor_id字段: {with_actor}/{total} ({with_actor/total*100:.1f}%)")
                print(f"   - 事件类型数量: {event_types}")
            
            # 统计各类事件的完整性
            cursor.execute("""
                SELECT 
                    event,
                    COUNT(*) as count,
                    SUM(CASE WHEN role IS NOT NULL AND role != '' THEN 1 ELSE 0 END) as complete_role,
                    SUM(CASE WHEN metadata IS NOT NULL AND metadata != '' THEN 1 ELSE 0 END) as complete_metadata
                FROM history 
                WHERE created_at > datetime('now', '-3 minute')
                GROUP BY event
                ORDER BY count DESC
            """)
            
            event_stats = cursor.fetchall()
            if event_stats:
                print(f"\n   📊 各事件类型完整性:")
                for event, count, complete_role, complete_metadata in event_stats:
                    role_rate = (complete_role / count * 100) if count > 0 else 0
                    meta_rate = (complete_metadata / count * 100) if count > 0 else 0
                    print(f"     - {event}: {count}条记录, role完整率{role_rate:.1f}%, metadata完整率{meta_rate:.1f}%")
            
            conn.close()
            
        except Exception as e:
            print(f"   数据库统计失败: {e}")
        
        # 测试结论
        print(f"\n🎯 测试结论:")
        if success_rate >= 95:
            print("   🟢 HTTP API数据一致性测试表现优秀，系统运行稳定")
            print("   🟢 历史记录系统修复效果显著，新记录数据完整")
        elif success_rate >= 80:
            print("   🟡 HTTP API数据一致性测试表现良好，有少量问题需要关注")
            print("   🟡 建议进一步检查失败的操作类型")
        else:
            print("   🔴 HTTP API数据一致性测试发现严重问题")
            print("   🔴 需要检查API实现和数据库同步机制")
        
        # 改进建议
        if total_failed > 0:
            print(f"\n💡 改进建议:")
            if self.test_results["add"]["failed"] > 0:
                print("   - 检查添加记忆的API实现和历史记录同步")
            if self.test_results["search"]["failed"] > 0:
                print("   - 验证搜索操作的历史记录机制")
            if self.test_results["update"]["failed"] > 0:
                print("   - 确认更新操作的数据一致性")
            if self.test_results["delete"]["failed"] > 0:
                print("   - 检查删除操作的历史记录完整性")
        
        return success_rate
    
    def run_full_test_suite(self) -> bool:
        """运行完整的测试套件"""
        print("🚀 开始HTTP API记忆操作数据一致性测试")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 API基础URL: {self.base_url}")
        print(f"👤 测试用户ID: {self.test_user_id}")
        
        try:
            # 1. 检查API健康状态
            if not self.check_api_health():
                print("❌ API服务不可用，测试终止")
                return False
            
            # 2. 测试添加操作
            add_success = self.test_add_memories_via_api()
            
            # 3. 测试搜索操作
            search_success = self.test_search_memories_via_api()
            
            # 4. 测试更新操作
            update_success = self.test_update_memories_via_api()
            
            # 5. 测试删除操作
            delete_success = self.test_delete_memories_via_api()
            
            # 6. 生成综合报告
            success_rate = self.generate_comprehensive_report()
            
            return success_rate >= 75  # 75%以上认为测试通过
            
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {e}")
            return False


def main():
    """主函数"""
    # 可以通过命令行参数指定API地址
    import sys
    
    api_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        api_url = sys.argv[1]
    
    print(f"🔧 初始化HTTP API测试客户端，目标: {api_url}")
    
    tester = HTTPMemoryAPIConsistencyTest(api_url)
    success = tester.run_full_test_suite()
    
    if success:
        print("\n🎉 HTTP API记忆操作数据一致性测试通过！")
        return 0
    else:
        print("\n⚠️  HTTP API记忆操作数据一致性测试发现问题！")
        return 1


if __name__ == "__main__":
    exit(main())